"""
Test fast launch mode performance improvements for lldb-dap.

This test validates that the fast launch mode achieves the target 120-400ms
launch times by deferring symbol loading and using on-demand loading.
"""

import dap_test_base
import lldbdap_testcase
import time
import os
from lldbsuite.test import lldbutil
from lldbsuite.test.decorators import *


class TestFastLaunch(lldbdap_testcase.DAPTestCaseBase):
    def setUp(self):
        lldbdap_testcase.DAPTestCaseBase.setUp(self)

    @skipIfWindows  # Skip on Windows due to different symbol loading behavior
    def test_fast_launch_performance(self):
        """Test that fast launch mode achieves target performance."""
        program = self.getBuildArtifact("a.out")
        self.build_and_create_debug_adaptor()
        
        # Test normal launch (baseline)
        start_time = time.time()
        response = self.dap_server.request_launch(
            program,
            stopOnEntry=True,
            # Normal launch - no fast mode
        )
        normal_launch_time = (time.time() - start_time) * 1000  # Convert to ms
        
        self.assertTrue(response['success'], 
                       f"Normal launch failed: {response.get('message', '')}")
        
        # Terminate and restart for fast launch test
        self.dap_server.request_disconnect(terminateDebuggee=True)
        self.create_debug_adaptor()
        
        # Test fast launch mode
        start_time = time.time()
        response = self.dap_server.request_launch(
            program,
            stopOnEntry=True,
            fastLaunchMode=True,
            deferSymbolLoading=True,
        )
        fast_launch_time = (time.time() - start_time) * 1000  # Convert to ms
        
        self.assertTrue(response['success'], 
                       f"Fast launch failed: {response.get('message', '')}")
        
        # Validate performance improvement
        print(f"Normal launch time: {normal_launch_time:.1f}ms")
        print(f"Fast launch time: {fast_launch_time:.1f}ms")
        
        # Fast launch should be significantly faster
        improvement_ratio = normal_launch_time / fast_launch_time
        print(f"Performance improvement: {improvement_ratio:.1f}x faster")
        
        # Target: Fast launch should be under 400ms (ideally ~180ms)
        self.assertLess(fast_launch_time, 400, 
                       f"Fast launch took {fast_launch_time:.1f}ms, "
                       f"target is <400ms")
        
        # Should be at least 2x faster than normal launch
        self.assertGreater(improvement_ratio, 2.0,
                          f"Fast launch only {improvement_ratio:.1f}x faster, "
                          f"expected at least 2x improvement")

    def test_fast_launch_debugging_functionality(self):
        """Test that fast launch mode preserves debugging functionality."""
        program = self.getBuildArtifact("a.out")
        self.build_and_create_debug_adaptor()
        
        source = "main.cpp"
        breakpoint_line = line_number(source, "// Set breakpoint here")
        
        # Launch with fast mode
        response = self.dap_server.request_launch(
            program,
            stopOnEntry=False,
            fastLaunchMode=True,
            deferSymbolLoading=True,
        )
        self.assertTrue(response['success'])
        
        # Set breakpoint - this should trigger symbol loading
        response = self.dap_server.request_setBreakpoints(source, [breakpoint_line])
        self.assertTrue(response['success'])
        self.assertEqual(len(response['body']['breakpoints']), 1)
        self.assertTrue(response['body']['breakpoints'][0]['verified'])
        
        # Continue and hit breakpoint
        self.dap_server.request_continue()
        stopped_event = self.dap_server.wait_for_stopped()
        self.assertEqual(stopped_event['body']['reason'], 'breakpoint')
        
        # Verify stack trace works (requires symbols)
        response = self.dap_server.request_stackTrace(stopped_event['body']['threadId'])
        self.assertTrue(response['success'])
        self.assertGreater(len(response['body']['stackFrames']), 0)
        
        # Verify variable inspection works
        frame_id = response['body']['stackFrames'][0]['id']
        response = self.dap_server.request_scopes(frame_id)
        self.assertTrue(response['success'])
        
        print("Fast launch debugging functionality verified")

    def test_background_symbol_loading(self):
        """Test that symbols are loaded in background after fast launch."""
        program = self.getBuildArtifact("a.out")
        self.build_and_create_debug_adaptor()
        
        # Launch with fast mode
        response = self.dap_server.request_launch(
            program,
            stopOnEntry=True,
            fastLaunchMode=True,
            deferSymbolLoading=True,
        )
        self.assertTrue(response['success'])
        
        # Should receive symbolsReady event eventually
        # Note: This is a custom event we added for background symbol loading
        events = []
        timeout = 10  # 10 second timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            event = self.dap_server.wait_for_event(['symbolsReady', 'stopped'], timeout=1)
            if event and event.get('event') == 'symbolsReady':
                print("Background symbol loading completed")
                break
            events.append(event)
        else:
            # It's okay if we don't get the event - symbols load on-demand
            print("Background symbol loading event not received (symbols load on-demand)")

if __name__ == '__main__':
    import unittest
    unittest.main()
