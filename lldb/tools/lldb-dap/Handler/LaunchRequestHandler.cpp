//===-- LaunchRequestHandler.cpp ------------------------------------------===//
//
// Part of the LLVM Project, under the Apache License v2.0 with LLVM Exceptions.
// See https://llvm.org/LICENSE.txt for license information.
// SPDX-License-Identifier: Apache-2.0 WITH LLVM-exception
//
//===----------------------------------------------------------------------===//

#include "DAP.h"
#include "EventHelper.h"
#include "JSONUtils.h"
#include "LLDBUtils.h"
#include "Protocol/ProtocolRequests.h"
#include "RequestHandler.h"
#include "llvm/Support/Error.h"
#include "llvm/Support/FileSystem.h"

using namespace llvm;
using namespace lldb_dap::protocol;

namespace lldb_dap {

/// Launch request; value of command field is 'launch'.
Error LaunchRequestHandler::Run(const LaunchRequestArguments &arguments) const {
  // Validate that we have a well formed launch request.
  if (!arguments.launchCommands.empty() &&
      arguments.console != protocol::eConsoleInternal)
    return make_error<DAPError>(
        "'launchCommands' and non-internal 'console' are mutually exclusive");

  dap.SetConfiguration(arguments.configuration, /*is_attach=*/false);
  dap.last_launch_request = arguments;

  // Log comprehensive performance optimization analysis
  DAP_LOG(dap.log, "=== LLDB DAP Launch Performance Analysis ===");
  DAP_LOG(dap.log, "Performance: Analyzing launch for program: {0}",
          arguments.configuration.program);

  if (dap.IsFastLaunchMode()) {
    DAP_LOG(dap.log,
            "Performance: Fast launch mode ENABLED - timeout: {0}ms (vs "
            "30000ms default)",
            dap.GetLaunchTimeoutMs());
    DAP_LOG(dap.log,
            "Performance: Fast launch mode reduces timeouts and enables "
            "optimizations");
  } else {
    DAP_LOG(dap.log,
            "Performance: Fast launch mode disabled - using timeout: {0}ms",
            dap.GetLaunchTimeoutMs());
  }

  if (dap.ShouldDeferSymbolLoading()) {
    DAP_LOG(dap.log, "Performance: Deferred symbol loading ENABLED");
    DAP_LOG(dap.log,
            "Performance: Deferred symbol loading can significantly improve "
            "target creation time");
  } else {
    DAP_LOG(dap.log, "Performance: Deferred symbol loading disabled - "
                     "loading all symbols during launch");
  }

  if (dap.ShouldUseLazyPluginLoading()) {
    DAP_LOG(dap.log, "Performance: Lazy plugin loading ENABLED");
    DAP_LOG(dap.log,
            "Performance: Lazy plugin loading defers non-essential plugin "
            "initialization");
  } else {
    DAP_LOG(dap.log, "Performance: Lazy plugin loading disabled - "
                     "loading all plugins during launch");
  }

  dap.EndPerformanceTiming("launch_configuration");
  PrintWelcomeMessage();

  // This is a hack for loading DWARF in .o files on Mac where the .o files
  // in the debug map of the main executable have relative paths which
  // require the lldb-dap binary to have its working directory set to that
  // relative root for the .o files in order to be able to load debug info.
  if (!dap.configuration.debuggerRoot.empty())
    sys::fs::set_current_path(dap.configuration.debuggerRoot);

  // Run any initialize LLDB commands the user specified in the launch.json.
  // This is run before target is created, so commands can't do anything with
  // the targets - preRunCommands are run with the target.
  if (Error err = dap.RunInitCommands())
    return err;

  dap.ConfigureSourceMaps();

  // Configure network symbol settings based on user preferences and network detection
  dap.ConfigureNetworkSymbolSettings();

  // Configure network symbol settings based on user preferences and network detection
  dap.ConfigureNetworkSymbolSettings();

  lldb::SBError error;
  // Use fast launch mode for immediate startup if enabled
  bool fast_launch = dap.IsFastLaunchMode() || dap.ShouldDeferSymbolLoading();
  lldb::SBTarget target = dap.CreateTarget(error, fast_launch);
  if (error.Fail())
    return ToError(error);

  dap.SetTarget(target);

  // Start background symbol loading if fast launch mode is enabled
  if (fast_launch) {
    DAP_LOG(dap.log, "Fast launch: Starting background symbol loading");
    dap.LoadSymbolsAsync();
  }

  // Run any pre run LLDB commands the user specified in the launch.json
  if (Error err = dap.RunPreRunCommands())
    return err;

  if (Error err = LaunchProcess(arguments))
    return err;

  dap.RunPostRunCommands();

  return Error::success();
}

void LaunchRequestHandler::PostRun() const {
  dap.SendJSON(CreateEventObject("initialized"));
}

} // namespace lldb_dap
